# -*- coding: utf-8 -*-
"""
会员消费数据图表生成模块
基于 NewMembersPic.py 的架构模式，生成会员消费数据的可视化图表和AI分析
"""

import datetime
import logging
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Tuple

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class MemberConsumptionPicGenerator:
    """会员消费数据图片生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图片生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数（对象或字典）
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_member_consumption_charts(self, query_params) -> Dict[str, str]:
        """
        生成会员消费数据图表

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 包含两张图片路径和AI分析的字典
        """
        try:
            logger.info(f"开始生成会员消费数据图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 计算时间范围
            current_date = datetime.datetime.now()
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            query_end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d")

            logger.info(f"时间参数 - 当前日期: {current_date}, 查询结束日期: {query_end_date}")

            # 计算去年和今年的时间范围
            last_year = query_end_date.year - 1
            this_year = query_end_date.year

            # 去年：完整的12个月
            last_year_ranges = self._generate_monthly_ranges(last_year, 1, 12)

            # 今年：从1月到查询结束月份（如果是当前年份，则到前一天）
            if this_year == current_date.year:
                # 如果是当前年份，计算到昨天
                yesterday = current_date - datetime.timedelta(days=1)
                end_month = yesterday.month
            else:
                # 如果不是当前年份，使用查询结束日期的月份
                end_month = query_end_date.month

            this_year_ranges = self._generate_monthly_ranges(this_year, 1, end_month)

            # 获取数据
            logger.info(f"开始获取数据 - 去年范围: {len(last_year_ranges)}个月, 今年范围: {len(this_year_ranges)}个月")
            last_year_data = await self._fetch_monthly_data(last_year_ranges, query_params)
            this_year_data = await self._fetch_monthly_data(this_year_ranges, query_params)

            logger.info(f"数据获取结果 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 初始化结果字典
            result = {}

            # 如果数据获取失败，生成错误图片
            if not last_year_data:
                logger.warning("去年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("member_consumption_last_year", "去年数据获取失败")
                if error_path:
                    result["member_consumption_last_year"] = error_path

            if not this_year_data:
                logger.warning("今年数据获取失败，生成错误图片")
                error_path = self._generate_error_image("member_consumption_this_year", "今年数据获取失败")
                if error_path:
                    result["member_consumption_this_year"] = error_path

            logger.info(f"最终数据 - 去年: {len(last_year_data)}条, 今年: {len(this_year_data)}条")

            # 只有在有有效数据时才生成正常图片
            if last_year_data and self._has_valid_consumption_data(last_year_data):
                last_year_path = await self._generate_chart(
                    last_year_data,
                    f"{last_year}年会员消费数据分析",
                    "member_consumption_last_year"
                )
                if last_year_path:
                    result["member_consumption_last_year"] = last_year_path

            if this_year_data and self._has_valid_consumption_data(this_year_data):
                this_year_path = await self._generate_chart(
                    this_year_data,
                    f"{this_year}年会员消费数据分析",
                    "member_consumption_this_year"
                )
                if this_year_path:
                    result["member_consumption_this_year"] = this_year_path

            # 生成AI分析
            try:
                from .PictureAi import PictureAiAnalyzer
                ai_analyzer = PictureAiAnalyzer()

                logger.info("开始生成会员消费数据AI分析...")

                # 无论数据是否完整，都尝试生成AI分析
                if last_year_data and this_year_data:
                    # 数据完整，生成完整分析
                    logger.info("数据完整，生成完整AI分析")
                    ai_analysis = await ai_analyzer.generate_all_member_consumption_analysis(this_year_data, last_year_data)
                    result.update(ai_analysis)
                elif this_year_data:
                    # 只有今年数据，生成今年分析和默认去年分析
                    logger.info("只有今年数据，生成部分AI分析")
                    this_year_analysis = await ai_analyzer.analyze_member_consumption_this_year_data(this_year_data, [])
                    result.update({
                        "member_consumption_last_year_analysis_report": "1、去年消费数据缺失，无法进行详细分析。\n2、建议完善数据收集机制，确保历史数据完整性。\n3、可通过其他渠道补充去年同期数据作为对比基准。\n4、重点关注今年消费趋势，制定针对性运营策略。",
                        "member_consumption_this_year_analysis_report": this_year_analysis
                    })
                elif last_year_data:
                    # 只有去年数据，生成去年分析和默认今年分析
                    logger.info("只有去年数据，生成部分AI分析")
                    last_year_analysis = await ai_analyzer.analyze_member_consumption_last_year_data(last_year_data)
                    result.update({
                        "member_consumption_last_year_analysis_report": last_year_analysis,
                        "member_consumption_this_year_analysis_report": "1、今年消费数据缺失，无法进行当期分析。\n2、建议立即启动数据收集，确保实时监控会员消费情况。\n3、基于去年数据制定今年消费目标和策略。\n4、加强数据统计和分析能力建设。"
                    })
                else:
                    # 数据都缺失，生成默认分析
                    logger.warning("数据完全缺失，生成默认AI分析")
                    result.update({
                        "member_consumption_last_year_analysis_report": "1、历史消费数据缺失，无法进行趋势分析。\n2、建议立即建立完善的数据收集和统计体系。\n3、制定数据恢复计划，尽可能补充历史数据。\n4、建立数据质量监控机制，确保未来数据完整性。",
                        "member_consumption_this_year_analysis_report": "1、当前消费数据缺失，无法评估会员消费情况。\n2、建议紧急启动数据收集工作，确保业务监控正常。\n3、制定应急数据获取方案，通过多渠道收集消费数据。\n4、建立数据备份和恢复机制，防止数据丢失。"
                    })

                logger.info("会员消费数据AI分析生成完成")

            except Exception as ai_error:
                logger.error(f"生成AI分析失败: {ai_error}")
                import traceback
                traceback.print_exc()
                result.update({
                    "member_consumption_last_year_analysis_report": "1、AI分析系统暂时不可用，请稍后重试。\n2、建议检查AI服务连接状态和配置。\n3、可暂时使用人工分析替代AI分析功能。\n4、联系技术支持解决AI分析问题。",
                    "member_consumption_this_year_analysis_report": "1、AI分析服务异常，无法生成智能分析报告。\n2、建议检查系统日志，排查AI服务故障原因。\n3、可使用历史分析模板进行手动分析。\n4、尽快恢复AI分析功能，确保报告质量。"
                })

            logger.info(f"会员消费数据图表和分析生成完成，共生成 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成会员消费数据图表失败: {e}")
            return {}

    def _has_valid_consumption_data(self, data: List[Dict[str, Any]]) -> bool:
        """
        检查是否有有效的消费数据

        Args:
            data: 月度数据列表

        Returns:
            bool: 是否有有效数据
        """
        if not data:
            return False

        # 检查是否有任何月份有消费金额
        for item in data:
            total_amount = item.get('total_actual_amount', 0)
            prepay_amount = item.get('prepay_actual_amount', 0)
            cash_amount = item.get('actual_amount', 0)

            if total_amount > 0 or prepay_amount > 0 or cash_amount > 0:
                return True

        return False

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建错误图片
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据生成失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    def _generate_monthly_ranges(self, year: int, start_month: int, end_month: int) -> List[Tuple[str, str, str]]:
        """
        生成月度时间范围

        Args:
            year: 年份
            start_month: 开始月份
            end_month: 结束月份

        Returns:
            List: [(月份标签, 开始日期, 结束日期), ...]
        """
        ranges = []

        for month in range(start_month, end_month + 1):
            # 计算月份的第一天和最后一天
            first_day = datetime.date(year, month, 1)

            # 计算下个月的第一天，然后减去一天得到当月最后一天
            if month == 12:
                next_month_first = datetime.date(year + 1, 1, 1)
            else:
                next_month_first = datetime.date(year, month + 1, 1)

            last_day = next_month_first - datetime.timedelta(days=1)

            # 如果是当前月份且是今年，需要截止到昨天
            if year == datetime.datetime.now().year and month == datetime.datetime.now().month:
                yesterday = datetime.datetime.now().date() - datetime.timedelta(days=1)
                if yesterday < last_day:
                    last_day = yesterday

            month_label = f"{year}年{month}月"
            start_date = first_day.strftime("%Y-%m-%d")
            end_date = last_day.strftime("%Y-%m-%d")

            ranges.append((month_label, start_date, end_date))

        return ranges

    async def _fetch_monthly_data(self, time_ranges: List[Tuple[str, str, str]], query_params) -> List[Dict[str, Any]]:
        """
        获取月度消费数据

        Args:
            time_ranges: 时间范围列表
            query_params: 查询参数

        Returns:
            List: 月度消费数据列表
        """
        try:
            from api.query.MemberConsumeSql import MemberConsumeSqlQueries
            from core.database import db

            monthly_data = []
            bid = self._extract_param(query_params, 'bid')
            sid = self._extract_param(query_params, 'sid', None)

            for month_label, start_date, end_date in time_ranges:
                logger.info(f"获取 {month_label} 消费数据: {start_date} 到 {end_date}")

                try:
                    # 转换日期格式为YYYYMMDD
                    start_date_db = start_date.replace('-', '')
                    end_date_db = end_date.replace('-', '')

                    # 1. 获取基础消费数据
                    base_sql = MemberConsumeSqlQueries.build_dwoutput_consume_base_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    base_result = await db.execute_dwoutput_one(base_sql)
                    base_result = base_result if base_result else {}

                    # 2. 获取储值使用详情
                    detail_sql = MemberConsumeSqlQueries.build_dwoutput_consume_detail_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    detail_result = await db.execute_dwoutput_one(detail_sql)
                    detail_result = detail_result if detail_result else {}

                    # 3. 获取券交易数据（可选）
                    coupon_sql = MemberConsumeSqlQueries.build_wedatas_coupon_trade_sql(
                        start_date_db, end_date_db, bid, sid
                    )
                    coupon_result = await db.execute_wedatas_one(coupon_sql)
                    coupon_result = coupon_result if coupon_result else {}

                    # 4. 直接提取所需字段并转换单位（避免merge_consume_data中的消费人数计算）
                    # 现金实收金额
                    cash_real = base_result.get('total_consume_cash_real', 0) or 0
                    actual_amount = float(cash_real) / 100 / 10000  # 转换为万元

                    # 储值使用实收金额
                    prepay_used_real = detail_result.get('total_prepay_used_real', 0) or 0
                    prepay_actual_amount = float(prepay_used_real) / 100 / 10000  # 转换为万元

                    # 总实收金额 = 现金实收 + 储值使用实收
                    total_actual_amount = actual_amount + prepay_actual_amount

                    # 计算占比
                    prepay_ratio = 0.0
                    cash_ratio = 0.0
                    if total_actual_amount > 0:
                        prepay_ratio = (prepay_actual_amount / total_actual_amount) * 100
                        cash_ratio = (actual_amount / total_actual_amount) * 100

                    logger.info(f"{month_label} 数据: 总实收{total_actual_amount:.2f}万元, 储值{prepay_actual_amount:.2f}万元, 现金{actual_amount:.2f}万元")

                    monthly_data.append({
                        'month': month_label,
                        'total_actual_amount': total_actual_amount,  # 会员总实收金额（万元）
                        'prepay_actual_amount': prepay_actual_amount,  # 会员使用储值的实收金额（万元）
                        'actual_amount': actual_amount,  # 会员现金消费实收金额（万元）
                        'prepay_ratio': prepay_ratio,  # 储值消费实收金额占比
                        'cash_ratio': cash_ratio  # 现金消费实收金额占比
                    })

                except Exception as month_error:
                    logger.error(f"获取 {month_label} 消费数据失败: {month_error}")
                    # 添加失败的月份数据，避免图表中断
                    monthly_data.append({
                        'month': month_label,
                        'total_actual_amount': 0.0,
                        'prepay_actual_amount': 0.0,
                        'actual_amount': 0.0,
                        'prepay_ratio': 0.0,
                        'cash_ratio': 0.0
                    })

            logger.info(f"消费数据获取完成，共 {len(monthly_data)} 个月的数据")
            return monthly_data

        except Exception as e:
            logger.error(f"获取月度消费数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成消费数据图表

        Args:
            data: 月度数据列表
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            if not data:
                logger.warning(f"数据为空，无法生成图表: {image_type}")
                return ""

            # 创建图表
            fig, ax1 = plt.subplots(figsize=(14, 8))

            # 提取数据
            months = [item['month'] for item in data]
            prepay_amounts = [item['prepay_actual_amount'] for item in data]
            cash_amounts = [item['actual_amount'] for item in data]

            # 设置X轴位置
            x_pos = range(len(months))

            # 绘制双色柱状图（储值和现金消费）
            bar_width = 0.35
            ax1.bar([x - bar_width/2 for x in x_pos], prepay_amounts,
                   bar_width, label='会员使用储值的实收金额（万元）', color='#E74C3C', alpha=0.8)
            ax1.bar([x + bar_width/2 for x in x_pos], cash_amounts,
                   bar_width, label='会员现金消费实收金额（万元）', color='#FF8C00', alpha=0.8)

            # 设置坐标轴
            ax1.set_xlabel('月份', fontsize=12)
            ax1.set_ylabel('金额（万元）', fontsize=12)
            ax1.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax1.set_xticks(x_pos)
            # 去掉年份前缀，只保留月份，水平显示
            month_labels = [month.split('-')[-1] + '月' if '-' in month else month for month in months]
            ax1.set_xticklabels(month_labels, rotation=0, ha='center')
            ax1.grid(True, alpha=0.3)

            # 设置图例
            ax1.legend(loc='upper left', fontsize=11)

            # 在柱状图上添加数值标签
            max_amount = max(max(prepay_amounts) if prepay_amounts else [0], max(cash_amounts) if cash_amounts else [0])
            for i, (prepay, cash) in enumerate(zip(prepay_amounts, cash_amounts)):
                if prepay > 0:
                    ax1.text(i - bar_width/2, prepay + max_amount * 0.02, f'{prepay:.2f}',
                            ha='center', va='bottom', fontsize=9)
                if cash > 0:
                    ax1.text(i + bar_width/2, cash + max_amount * 0.02, f'{cash:.2f}',
                            ha='center', va='bottom', fontsize=9)

            # 添加数据表格
            self._add_consumption_data_table(data, ax1)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            save_path = self.image_manager.get_image_path(image_type)
            logger.info(f"准备保存图片到: {save_path}")

            # 确保保存目录存在
            from pathlib import Path
            save_dir = Path(save_path).parent
            save_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"保存目录: {save_dir}, 存在: {save_dir.exists()}")

            # 保存图片
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                logger.info(f"matplotlib保存完成")

                # 验证文件是否真的被创建
                if Path(save_path).exists():
                    file_size = Path(save_path).stat().st_size
                    logger.info(f"图片保存成功: {save_path}, 大小: {file_size} bytes")
                    plt.close(fig)
                    return save_path
                else:
                    logger.error(f"图片文件未创建: {save_path}")
                    plt.close(fig)
                    return ""

            except Exception as save_error:
                logger.error(f"保存图片失败: {save_error}")
                plt.close(fig)
                return ""

        except Exception as e:
            logger.error(f"生成消费数据图表失败: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def _add_consumption_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加消费数据表格（横向布局，时间为横轴）

        Args:
            data: 消费数据列表
            ax: 坐标轴对象
        """
        try:
            # 准备横向表格数据
            months = [item['month'] for item in data]
            total_amounts = [f"{item['total_actual_amount']:.2f}" for item in data]
            prepay_amounts = [f"{item['prepay_actual_amount']:.2f}" for item in data]
            cash_amounts = [f"{item['actual_amount']:.2f}" for item in data]

            # 构建横向表格数据：第一行是月份，后面三行是数据
            table_data = [
                months,  # 第一行：月份
                total_amounts,  # 第二行：总实收金额
                prepay_amounts,  # 第三行：储值实收金额
                cash_amounts,  # 第四行：现金实收金额
            ]

            # 行标题（左侧标签）
            row_labels = ['月份', '总实收(万元)', '储值实收(万元)', '现金实收(万元)']

            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.6, 1, 0.4]  # [x, y, width, height] - 调整高度以容纳4行数据
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(8)  # 调整字体大小
            table.scale(1, 1.2)

            # 设置行标题样式（左侧列）
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(months)):
                    if i == 0:  # 月份行使用浅蓝色
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行使用浅灰色
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行使用白色
                        table[(i, j)].set_facecolor('white')

            logger.info(f"消费数据表格创建完成，包含 {len(months)} 个月的数据")

        except Exception as e:
            logger.error(f"添加消费数据表格失败: {e}")


def create_member_consumption_pic_generator(bid: str, image_manager) -> MemberConsumptionPicGenerator:
    """
    创建会员消费数据图片生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        MemberConsumptionPicGenerator: 图片生成器实例
    """
    return MemberConsumptionPicGenerator(bid, image_manager)