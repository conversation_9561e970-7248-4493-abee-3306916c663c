# -*- coding: utf-8 -*-
"""
PPT报告参数配置文件
定义PPT模板参数常量和数据映射关系
"""

import datetime
import logging
from typing import Dict, Any, Optional

from .PictureConstants import get_image_params

logger = logging.getLogger(__name__)

class PPTConstants:
    """PPT参数常量类"""

    # ==================== PPT第1页 - 基础配置 ====================

    @staticmethod
    def get_basic_config(time_frame: str = None) -> Dict[str, Any]:
        """获取基础配置参数"""
        return {
            "time_frame": time_frame or "2024年1月-12月",
            "report_date": datetime.datetime.now().strftime("%Y年%m月%d日")
        }

    # ==================== PPT第6页 - 会员数据分析报告 ====================

    @staticmethod
    def get_member_data_params(member_base_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从会员基础数据生成PPT参数

        Args:
            member_base_data: 从query模块获取的会员基础数据

        Returns:
            Dict: PPT参数字典
        """
        if not member_base_data:
            logger.warning("会员基础数据为空，使用默认值")
            return {"member_data_analysis_report": "会员数据分析报告"}

        # 从API数据中提取值，使用安全的取值方式
        def safe_get_value(data: Dict[str, Any], field: str, default: int = 0) -> int:
            """安全获取数据值"""
            field_data = data.get(field, {})
            if isinstance(field_data, dict):
                return int(field_data.get('value', default))
            return int(field_data) if field_data else default

        # 按照用户提供的映射关系提取数据
        total_members = safe_get_value(member_base_data, 'total_members')  # 1.1
        new_members = safe_get_value(member_base_data, 'new_members')  # 1.3
        complete_phone_members = safe_get_value(member_base_data, 'complete_phone_members')  # 1.17
        total_complete_members = safe_get_value(member_base_data, 'total_complete_members')  # 1.13
        # 计算总消费会员数：消费1次的会员数 + 消费2次以上的会员数
        consume_once_members = safe_get_value(member_base_data, 'consume_once_members')  # 来自MemberBaseTab.py映射
        consume_multiple_members = safe_get_value(member_base_data, 'consume_multiple_members')  # 来自MemberBaseTab.py映射
        total_consume_members = consume_once_members + consume_multiple_members  # 1.9
        total_charge_members = safe_get_value(member_base_data, 'total_charge_members')  # 1.10

        # 新增字段：累计取关会员数和现存会员净量
        unfollow_members = safe_get_value(member_base_data, 'unfollow_members')  # 累计取关会员数 (对应total_cancel_user)
        net_members = safe_get_value(member_base_data, 'net_members')  # 现存会员净量 (对应net_all_user)

        # 计算派生字段
        historical_members = total_members - new_members
        complete_nophone_members = total_members - complete_phone_members
        total_nocomplete_members = total_members - total_complete_members
        total_noconsume_members = total_members - total_consume_members
        total_nocharge_members = total_members - total_charge_members

        params = {
            "member_data_analysis_report": "会员数据分析报告",
            "total_members": str(total_members),
            "new_members": str(new_members),
            "historical_members": str(historical_members),
            "complete_phone_members": str(complete_phone_members),
            "complete_nophone_members": str(complete_nophone_members),
            "total_complete_members": str(total_complete_members),
            "total_nocomplete_members": str(total_nocomplete_members),
            "total_consume_members": str(total_consume_members),
            "total_noconsume_members": str(total_noconsume_members),
            "total_charge_members": str(total_charge_members),
            "total_nocharge_members": str(total_nocharge_members),
            # 新增参数
            "unfollow_members": str(unfollow_members),  # 累计取关会员数
            "net_members": str(net_members)  # 现存会员净量
        }

        return params

    # ==================== PPT第8页 - 会员收入分析报告 ====================

    @staticmethod
    def get_revenue_analysis_params(member_consume_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从会员消费数据生成收入分析参数

        Args:
            member_consume_data: 从query模块获取的会员消费数据

        Returns:
            Dict: PPT参数字典
        """
        params = {
            "member_revenue_analysis_report": "会员收入分析报告"
        }

        if member_consume_data:
            # 安全获取数据值的辅助函数
            def safe_get_float_value(data: Dict[str, Any], field: str, default: float = 0.0) -> float:
                """安全获取浮点数值"""
                field_data = data.get(field, {})
                if isinstance(field_data, dict):
                    return float(field_data.get('value', default))
                return float(field_data) if field_data else default

            # 按照用户提供的映射关系提取数据
            # 2.12. 会员总实收金额 -> total_actual_amount
            total_actual_amount = safe_get_float_value(member_consume_data, "total_actual_amount")
            params["total_actual_amount"] = f"{total_actual_amount:,.2f}"

            # 2.10. 会员使用储值的实收金额 -> prepay_actual_amount
            prepay_actual_amount = safe_get_float_value(member_consume_data, "prepay_actual_amount")
            params["prepay_actual_amount"] = f"{prepay_actual_amount:,.2f}"

            # 2.4. 会员实收金额 -> actual_amount
            actual_amount = safe_get_float_value(member_consume_data, "actual_amount")
            params["actual_amount"] = f"{actual_amount:,.2f}"

            # 2.16. 会员消费频次 -> consume_frequency
            consume_frequency = safe_get_float_value(member_consume_data, "consume_frequency")
            params["consume_frequency"] = f"{consume_frequency:.2f}"

            # 2.15. 会员人均贡献 -> avg_contribution
            avg_contribution = safe_get_float_value(member_consume_data, "avg_contribution")
            params["avg_contribution"] = f"{avg_contribution:.2f}"

            # 2.17. 会员单均消费 -> avg_consume_amount (PPT第八页使用)
            avg_consume_amount = safe_get_float_value(member_consume_data, "avg_consume_amount")
            params["avg_consume_amount"] = f"{avg_consume_amount:.2f}"

            # 2.2. 会员消费人数 -> consume_users
            consume_users = int(safe_get_float_value(member_consume_data, "consume_users"))
            params["consume_users"] = str(consume_users)

            # 2.13. 首次消费金额 -> first_consume_amount
            first_consume_amount = safe_get_float_value(member_consume_data, "first_consume_amount")
            params["first_consume_amount"] = f"{first_consume_amount:,.2f}"

            # 2.14. 再次消费金额 -> repeat_consume_amount
            repeat_consume_amount = safe_get_float_value(member_consume_data, "repeat_consume_amount")
            params["repeat_consume_amount"] = f"{repeat_consume_amount:,.2f}"

        return params

    # ==================== PPT第8页 - 品智收银数据分析 ====================

    @staticmethod
    def get_pinzhi_cashier_params(pinzhi_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从品智收银数据生成PPT参数

        Args:
            pinzhi_data: 从PinzhiData服务获取的品智收银数据

        Returns:
            Dict: PPT参数字典，包含7个品智收银字段
        """
        params = {}

        if not pinzhi_data:
            logger.warning("品智收银数据为空，使用默认值")
            return {
                "total_actual_revenue": "0.00",
                "total_expected_revenue": "0.00",
                "dine_in_actual_revenue": "0.00",
                "takeout_actual_revenue": "0.00",
                "non_member_total_actual_amount": "0.00",
                "discount_rate": "0.00%",
                "member_dine_in_ratio": "0.00%"
            }

        # 安全获取数据值的辅助函数
        def safe_get_float_value(data: Dict[str, Any], field: str, default: float = 0.0) -> float:
            """安全获取浮点数值"""
            try:
                return float(data.get(field, default))
            except (ValueError, TypeError):
                logger.warning(f"品智收银字段 {field} 值转换失败，使用默认值")
                return default

        # 按照用户提供的映射关系提取数据
        # 营业额实收
        total_actual_revenue = safe_get_float_value(pinzhi_data, "total_actual_revenue")
        params["total_actual_revenue"] = f"{total_actual_revenue:,.2f}"

        # 营业额应收
        total_expected_revenue = safe_get_float_value(pinzhi_data, "total_expected_revenue")
        params["total_expected_revenue"] = f"{total_expected_revenue:,.2f}"

        # 堂食实收
        dine_in_actual_revenue = safe_get_float_value(pinzhi_data, "dine_in_actual_revenue")
        params["dine_in_actual_revenue"] = f"{dine_in_actual_revenue:,.2f}"

        # 外卖实收
        takeout_actual_revenue = safe_get_float_value(pinzhi_data, "takeout_actual_revenue")
        params["takeout_actual_revenue"] = f"{takeout_actual_revenue:,.2f}"

        # 非会员实收
        non_member_total_actual_amount = safe_get_float_value(pinzhi_data, "non_member_total_actual_amount")
        params["non_member_total_actual_amount"] = f"{non_member_total_actual_amount:,.2f}"

        # 折扣率（转换为百分比格式）
        discount_rate = safe_get_float_value(pinzhi_data, "discount_rate")
        params["discount_rate"] = f"{discount_rate:.2f}%"

        # 会员消费占比（转换为百分比格式）
        member_dine_in_ratio = safe_get_float_value(pinzhi_data, "member_dine_in_ratio")
        params["member_dine_in_ratio"] = f"{member_dine_in_ratio:.2f}%"

        logger.info(f"品智收银参数生成完成: {params}")
        return params

    # ==================== PPT第18页 - 优惠券数据分析 ====================

    @staticmethod
    def get_coupon_analysis_params(coupon_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从优惠券数据生成分析参数

        优惠券排序逻辑：
        - 优惠券1-10应该按照 `coupon_send_count`（发券量）从高到低排序选择
        - 从 CouponTradeTab.py 模块的字段 `couponSendCount` 获取数据
        - 当有超过10个优惠券时，选择发券量最高的前10个优惠券进行展示
        - 汇总统计基于所有券计算，而非仅展示的前10个券

        Args:
            coupon_data: 从query模块获取的优惠券数据（包含展示数据和汇总统计）

        Returns:
            Dict: PPT参数字典
        """
        params = {}

        if not coupon_data:
            logger.warning("优惠券数据为空，使用默认值")
            return params

        # 获取优惠券列表数据（已按发券量降序排序）
        coupon_list = coupon_data.get("coupon_list", [])
        # 获取基于所有券的汇总统计数据
        summary_stats = coupon_data.get("summary_stats", {})

        # 处理前10个优惠券的数据（按发券量从高到低排序）
        # 确保总是生成10个完整的优惠券参数集
        for i in range(1, 11):  # 1到10
            if i <= len(coupon_list) and coupon_list[i-1]:
                # 有实际优惠券数据
                coupon = coupon_list[i-1]

                # 按照用户提供的映射关系提取数据
                coupon_id = coupon.get('couponId', f'CPN{i:03d}')  # 4.2
                coupon_name = coupon.get('couponName', f'优惠券{i}')  # 4.1
                send_count = int(coupon.get('couponSendCount', 0))  # 4.3
                used_count = int(coupon.get('couponUsedCount', 0))  # 4.4
                usage_rate = float(coupon.get('couponUsageRate', 0))  # 4.5
                drive_total_amount = float(coupon.get('driveTotalAmount', 0))  # 4.9 带动总交易金额

                # 如果使用率为0，则计算使用率
                if usage_rate == 0 and send_count > 0:
                    usage_rate = (used_count / send_count) * 100
            else:
                # 没有实际优惠券数据，使用空值
                coupon_id = ""
                coupon_name = ""
                send_count = 0
                used_count = 0
                usage_rate = 0
                drive_total_amount = 0

            # 设置优惠券参数（无论是否有数据都要设置）
            params[f"coupon_id{i}"] = coupon_id
            params[f"coupon_name{i}"] = coupon_name
            params[f"coupon_send_count{i}"] = f"{send_count}张" if send_count > 0 else ""
            params[f"coupon_used_count{i}"] = f"{used_count}张" if used_count > 0 else ""
            params[f"coupon_usage_rate{i}"] = f"{usage_rate:.1f}%" if usage_rate > 0 else ""
            params[f"drive_total_amount{i}"] = f"{drive_total_amount:,.2f}元" if drive_total_amount > 0 else ""

        # 使用基于所有券的汇总统计数据
        total_send_count = summary_stats.get("total_send_count", 0)
        total_used_count = summary_stats.get("total_used_count", 0)
        total_drive_amount = summary_stats.get("total_drive_amount", 0.0)
        avg_usage_rate = summary_stats.get("avg_usage_rate", 0.0)

        # 设置汇总参数（添加单位）
        params["total_send_count"] = f"{total_send_count}张"
        params["total_used_count"] = f"{total_used_count}张"
        params["avg_usage_rate"] = f"{avg_usage_rate:.1f}%"
        params["total_drive_amount"] = f"{total_drive_amount:,.2f}元"

        # 记录生成的优惠券参数数量
        coupon_param_count = len([k for k in params.keys() if k.startswith('coupon_')])
        logger.info(f"生成优惠券参数完成 - 实际优惠券数量: {len(coupon_list)}, 生成参数数量: {coupon_param_count}")
        logger.info(f"汇总统计 - 总发券量: {total_send_count}张, 总使用量: {total_used_count}张, 平均使用率: {avg_usage_rate:.1f}%")

        return params



    # ==================== 数据整合方法 ====================

    @staticmethod
    async def build_complete_ppt_data(
        time_frame: str,
        member_base_data: Dict[str, Any] = None,
        member_consume_data: Dict[str, Any] = None,
        member_charge_data: Dict[str, Any] = None,
        coupon_data: Dict[str, Any] = None,
        pinzhi_data: Dict[str, Any] = None,
        industry_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        整合所有数据生成完整的PPT参数字典

        Args:
            time_frame: 时间范围
            member_base_data: 会员基础数据
            member_consume_data: 会员消费数据
            member_charge_data: 会员充值数据
            coupon_data: 优惠券数据
            pinzhi_data: 品智收银数据
            industry_data: 行业分析数据

        Returns:
            Dict: 完整的PPT参数字典
        """
        # 初始化完整参数字典
        complete_data = {}

        # 1. 基础配置
        complete_data.update(PPTConstants.get_basic_config(time_frame))

        # 2. 会员数据分析
        if member_base_data:
            member_params = PPTConstants.get_member_data_params(member_base_data)
            complete_data.update(member_params)

            # 如果有消费数据，更新消费相关参数
            if member_consume_data:
                # 使用与get_member_data_params相同的计算逻辑：consume_once_members + consume_multiple_members
                total_members = int(member_base_data.get("total_members", 0))
                consume_once_members = int(member_base_data.get("consume_once_members", 0))  # 来自MemberBaseTab.py映射
                consume_multiple_members = int(member_base_data.get("consume_multiple_members", 0))  # 来自MemberBaseTab.py映射
                total_consume_members = consume_once_members + consume_multiple_members
                complete_data["total_consume_members"] = str(total_consume_members)
                complete_data["total_noconsume_members"] = str(total_members - total_consume_members)

            # 如果有充值数据，更新充值相关参数
            if member_charge_data:
                # 充值用户数应该从会员基础数据中获取，而不是充值数据
                # 因为MemberChargeData模型中没有用户数字段，只有金额和笔数
                total_members = int(member_base_data.get("total_members", 0))
                charge_users = int(member_base_data.get("total_charge_members", 0))
                complete_data["total_charge_members"] = str(charge_users)
                complete_data["total_nocharge_members"] = str(total_members - charge_users)

        # 3. 收入分析
        if member_consume_data:
            revenue_params = PPTConstants.get_revenue_analysis_params(member_consume_data)
            complete_data.update(revenue_params)

        # 4. 优惠券分析
        if coupon_data:
            coupon_params = PPTConstants.get_coupon_analysis_params(coupon_data)
            complete_data.update(coupon_params)

        # 5. 品智收银分析
        if pinzhi_data:
            pinzhi_params = PPTConstants.get_pinzhi_cashier_params(pinzhi_data)
            complete_data.update(pinzhi_params)
            logger.info("品智收银数据已集成到PPT参数中")

        # 6. 行业分析数据（第19页PPT）
        if industry_data:
            logger.info(f"开始处理行业分析数据，数据量: {len(industry_data)}")
            industry_params = PPTConstants.get_industry_analysis_params(industry_data)
            complete_data.update(industry_params)
            logger.info(f"行业分析数据已集成到PPT参数中，新增参数: {len(industry_params)}")
        else:
            logger.warning("行业分析数据为空，跳过第19页PPT数据集成")

        # 7. AI分析结果
        try:
            from .PPTAi import PPTAiAnalyzer

            # 创建AI分析器实例
            ai_analyzer = PPTAiAnalyzer()

            # 生成AI分析结果
            if member_base_data:
                member_analysis = await ai_analyzer.analyze_member_data_report(complete_data)
                complete_data["member_data_analysis_report"] = member_analysis

            if member_consume_data:
                revenue_analysis = await ai_analyzer.analyze_member_revenue_report(complete_data)
                complete_data["member_revenue_analysis_report"] = revenue_analysis

        except Exception as e:
            logger.error(f"AI分析失败: {str(e)}")
            # 使用备用分析结果
            complete_data["member_data_analysis_report"] = """1、会员增长情况需要进一步分析，建议加强新用户获取和留存策略。
2、会员信息完善度有待提升，建议优化信息收集流程和激励机制。
3、会员活跃度表现一般，建议制定针对性的激活和转化方案。"""
            complete_data["member_revenue_analysis_report"] = """1、收入结构需要优化，建议平衡储值和现金消费比例。
2、消费频次和客单价有提升空间，建议推出促销和增值服务。
3、用户复购率需要改善，建议建立会员忠诚度计划。"""

        # 7. 图片参数（注意：不要覆盖已有的AI分析参数）
        # 只获取图片路径参数，不包含AI分析参数，避免覆盖NewMembersPic生成的AI分析
        from .PictureConstants import PictureConstants
        static_image_params = PictureConstants.get_image_params()

        # 只添加图片路径参数，跳过AI分析参数
        for key, value in static_image_params.items():
            if key.startswith("image_") and key not in complete_data:
                complete_data[key] = value

        return complete_data

    @staticmethod
    def validate_required_params(data_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证必需的PPT参数是否存在

        Args:
            data_dict: PPT参数字典

        Returns:
            Dict: 验证结果
        """
        # 定义必需的参数列表
        required_params = [
            "time_frame",
            "report_date",
            "member_data_analysis_report",
            "total_members",
            "new_members",
            "member_revenue_analysis_report"
        ]

        missing_params = []
        for param in required_params:
            if param not in data_dict or data_dict[param] is None:
                missing_params.append(param)

        return {
            "valid": len(missing_params) == 0,
            "missing_params": missing_params,
            "total_params": len(data_dict),
            "required_params": required_params
        }

    @staticmethod
    def get_default_data() -> Dict[str, Any]:
        """
        获取默认的PPT数据（用于测试或数据缺失时的备用）

        Returns:
            Dict: 默认PPT参数字典
        """
        return {
            # 基础配置
            "time_frame": "2024年1月-12月",
            "report_date": datetime.datetime.now().strftime("%Y年%m月%d日"),

            # 会员数据
            "member_data_analysis_report": "会员数据分析报告",
            "total_members": "15680",
            "new_members": "2340",
            "historical_members": "13340",
            "complete_phone_members": "14520",
            "complete_nophone_members": "1160",
            "total_complete_members": "15200",
            "total_nocomplete_members": "480",
            "total_consume_members": "8960",
            "total_noconsume_members": "6720",
            "total_charge_members": "5680",
            "total_nocharge_members": "10000",

            # 收入分析
            "member_revenue_analysis_report": "会员收入分析报告",
            "total_actual_amount": "1,405,632.50",
            "prepay_actual_amount": "892,345.60",
            "actual_amount": "1,405,632.50",
            "consume_frequency": "3.20",
            "avg_contribution": "502.40",  # 会员人均贡献（元）
            "avg_consume_amount": "156.80",  # 会员单均消费（元/笔）
            "consume_users": "8960",
            "first_consume_amount": "234,567.80",
            "repeat_consume_amount": "1,171,064.70",

            # 优惠券数据（示例）
            "avg_usage_rate": "68.5%",
            "total_drive_amount": "2,156,789.40",
            "total_send_count": "47000",
            "total_used_count": "30400",

            # AI分析结果
            "member_data_analysis_report": """1、新增会员占比14.9%表现良好，但信息完善度仅96.9%，建议优化注册流程提升数据收集效率。
2、消费转化率57.1%偏低，建议针对未消费会员制定激活策略，提升首次消费转化。
3、充值参与度36.2%有待提升，建议推出储值优惠政策，增强用户粘性和预付费意愿。""",

            "member_revenue_analysis_report": """1、储值消费占比63.5%表现优秀，但消费频次3.2次偏低，建议推出会员积分计划提升复购。
2、人均消费156.8元处于中等水平，建议通过套餐组合和增值服务提升客单价。
3、重复消费占比83.3%显示用户忠诚度高，建议加强老客户维护和转介绍激励。""",

            # 图片参数
            **get_image_params()
        }

    @staticmethod
    def get_industry_analysis_params(industry_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从行业分析数据生成PPT参数（第19页）

        Args:
            industry_data: 从IndustryDataService获取的行业分析数据

        Returns:
            Dict: PPT参数字典，包含当前时间段和年度数据
        """
        params = {}

        if not industry_data:
            logger.warning("行业分析数据为空，使用默认值")
            return params

        # 安全获取数据值的辅助函数
        def safe_get_percentage(data: Dict[str, Any], field: str, default: float = 0.0) -> str:
            """安全获取百分比值并格式化"""
            try:
                value = float(data.get(field, default))
                return f"{value * 100:.2f}%"
            except (ValueError, TypeError):
                logger.warning(f"行业分析字段 {field} 值转换失败，使用默认值")
                return f"{default * 100:.2f}%"

        def safe_get_float(data: Dict[str, Any], field: str, default: float = 0.0) -> str:
            """安全获取浮点数值并格式化"""
            try:
                value = float(data.get(field, default))
                return f"{value:.2f}"
            except (ValueError, TypeError):
                logger.warning(f"行业分析字段 {field} 值转换失败，使用默认值")
                return f"{default:.2f}"

        # 当前时间段数据
        params["phone_member_ratio"] = safe_get_float(industry_data, 'phone_member_ratio') + "%"
        params["prepay_member_ratio"] = safe_get_float(industry_data, 'prepay_member_ratio') + "%"
        params["consume_member_ratio"] = safe_get_float(industry_data, 'consume_member_ratio') + "%"
        params["consume_once_members_ratio"] = safe_get_percentage(industry_data, 'consume_once_members_ratio')
        params["consume_twice_members_ratio"] = safe_get_percentage(industry_data, 'consume_twice_members_ratio')
        params["consume_thrice_members_ratio"] = safe_get_percentage(industry_data, 'consume_thrice_members_ratio')
        params["consume_more_than_thrice_members_ratio"] = safe_get_percentage(industry_data, 'consume_more_than_thrice_members_ratio')
        params["repurchase_rate"] = safe_get_float(industry_data, 'repurchase_rate') + "%"
        params["consume_frequency"] = safe_get_float(industry_data, 'consume_frequency')
        params["prepay_consumption_ratio"] = safe_get_float(industry_data, 'prepay_consumption_ratio') + "%"
        params["prepay_retention_rate"] = safe_get_float(industry_data, 'prepay_retention_rate') + "%"
        params["coupon_usage_rate"] = safe_get_percentage(industry_data, 'coupon_usage_rate')

        # 年度数据（带_year后缀）
        params["phone_member_ratio_year"] = safe_get_float(industry_data, 'phone_member_ratio_year') + "%"
        params["prepay_member_ratio_year"] = safe_get_float(industry_data, 'prepay_member_ratio_year') + "%"
        params["consume_member_ratio_year"] = safe_get_float(industry_data, 'consume_member_ratio_year') + "%"
        params["consume_once_members_ratio_year"] = safe_get_percentage(industry_data, 'consume_once_members_ratio_year')
        params["consume_twice_members_ratio_year"] = safe_get_percentage(industry_data, 'consume_twice_members_ratio_year')
        params["consume_thrice_members_ratio_year"] = safe_get_percentage(industry_data, 'consume_thrice_members_ratio_year')
        params["consume_more_than_thrice_members_ratio_year"] = safe_get_percentage(industry_data, 'consume_more_than_thrice_members_ratio_year')
        params["repurchase_rate_year"] = safe_get_float(industry_data, 'repurchase_rate_year') + "%"
        params["consume_frequency_year"] = safe_get_float(industry_data, 'consume_frequency_year')
        params["prepay_consumption_ratio_year"] = safe_get_float(industry_data, 'prepay_consumption_ratio_year') + "%"
        params["prepay_retention_rate_year"] = safe_get_float(industry_data, 'prepay_retention_rate_year') + "%"
        params["coupon_usage_rate_year"] = safe_get_percentage(industry_data, 'coupon_usage_rate_year')

        logger.info(f"行业分析PPT参数生成完成，共{len(params)}个参数")
        return params