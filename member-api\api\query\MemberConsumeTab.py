from fastapi import APIRouter, HTTPException, Depends
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime, timedelta
import asyncio
import time

from core.database import db
from core.models import QueryParams, MemberConsumeData, FieldDataModel, ResponseModel
from constant import MEMBER_CONSUME_MODULE
from api.query.MemberConsumeSql import MemberConsumeSqlQueries, MemberConsumeCalculator
from api.query.MemberConsumeSqlAdd import MemberConsumeSqlAddQueries, MemberConsumeCalculatorAdd

logger = logging.getLogger(__name__)

router = APIRouter()

class MemberConsumeService:
    """会员消费数据服务"""
    
    def __init__(self):
        self.module_config = MEMBER_CONSUME_MODULE
        self.sql_queries = MemberConsumeSqlQueries()
        self.calculator = MemberConsumeCalculator()
    
    def _calculate_time_ranges(self, query_params: QueryParams) -> Dict[str, Any]:
        """计算时间范围，包括当前期间、环比期间、同比期间"""
        start_date = datetime.strptime(query_params.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(query_params.end_date, "%Y-%m-%d")
        
        # 计算期间长度
        period_days = (end_date - start_date).days + 1
        
        # 环比期间（上一个相同长度的期间）
        chain_end_date = start_date - timedelta(days=1)
        chain_start_date = chain_end_date - timedelta(days=period_days - 1)
        
        # 同比期间（去年同期）
        year_over_year_start = start_date.replace(year=start_date.year - 1)
        year_over_year_end = end_date.replace(year=end_date.year - 1)
        
        return {
            "current": {
                "start": start_date.strftime("%Y%m%d"),
                "end": end_date.strftime("%Y%m%d"),
                "label": "本期"
            },
            "chain": {
                "start": chain_start_date.strftime("%Y%m%d"),
                "end": chain_end_date.strftime("%Y%m%d"),
                "label": "上期"
            },
            "year_over_year": {
                "start": year_over_year_start.strftime("%Y%m%d"),
                "end": year_over_year_end.strftime("%Y%m%d"),
                "label": "去年同期"
            }
        }
    
    async def _fetch_dwoutput_consume_base_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取dwoutput数据库的消费基础数据 - 独立查询版本"""
        try:
            logger.info(f"开始查询dwoutput消费基础数据 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            start_time = time.time()
            
            # 独立执行每个消费基础数据查询
            result = {}
            sid_condition = f"AND sid = '{sid}'" if sid else ""
            base_from_where = f"""
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN {start_date} AND {end_date}
            AND bid = {bid}
            {sid_condition}
            """
            
            # 逐个执行查询（除了total_consume_uv、total_consume_cash_uv、total_prepay_uv，它们需要单独处理）
            queries = [
                ('total_consume_amount_virtual', MemberConsumeSqlQueries.get_dwoutput_total_consume_amount_virtual_sql(start_date, end_date, bid, sid)),
                ('total_consume_pv', MemberConsumeSqlQueries.get_dwoutput_total_consume_pv_sql(start_date, end_date, bid, sid)),
                ('total_consume_cash_real', MemberConsumeSqlQueries.get_dwoutput_total_consume_cash_real_sql(start_date, end_date, bid, sid)),
                ('total_consume_cash_pv', MemberConsumeSqlQueries.get_dwoutput_total_consume_cash_pv_sql(start_date, end_date, bid, sid)),
                ('total_prepay_real', MemberConsumeSqlQueries.get_dwoutput_total_prepay_real_sql(start_date, end_date, bid, sid)),
                ('total_prepay_pv', MemberConsumeSqlQueries.get_dwoutput_total_prepay_pv_sql(start_date, end_date, bid, sid))
            ]

            for field_name, field_sql in queries:
                full_sql = f"SELECT {field_sql} {base_from_where}"
                logger.info(f"{field_name}查询SQL: {full_sql}")
                field_result = await db.execute_dwoutput_one(full_sql)
                result[field_name] = field_result.get(field_name, 0) if field_result else 0

            # 单独查询会员消费人数（使用明细表数据源）
            consume_uv_sql = MemberConsumeSqlQueries.get_dwoutput_total_consume_uv_sql(start_date, end_date, bid, sid)
            logger.info(f"total_consume_uv查询SQL: {consume_uv_sql}")
            consume_uv_result = await db.execute_dwoutput_one(consume_uv_sql)
            result['total_consume_uv'] = consume_uv_result.get('total_consume_uv', 0) if consume_uv_result else 0

            # 单独查询现金支付会员数（使用明细表数据源）
            consume_cash_uv_sql = MemberConsumeSqlQueries.get_dwoutput_total_consume_cash_uv_sql(start_date, end_date, bid, sid)
            logger.info(f"total_consume_cash_uv查询SQL: {consume_cash_uv_sql}")
            consume_cash_uv_result = await db.execute_dwoutput_one(consume_cash_uv_sql)
            result['total_consume_cash_uv'] = consume_cash_uv_result.get('total_consume_cash_uv', 0) if consume_cash_uv_result else 0

            # 单独查询储值支付会员数（使用明细表数据源）
            prepay_uv_sql = MemberConsumeSqlQueries.get_dwoutput_total_prepay_uv_sql(start_date, end_date, bid, sid)
            logger.info(f"total_prepay_uv查询SQL: {prepay_uv_sql}")
            prepay_uv_result = await db.execute_dwoutput_one(prepay_uv_sql)
            result['total_prepay_uv'] = prepay_uv_result.get('total_prepay_uv', 0) if prepay_uv_result else 0



            # 单独查询首次消费金额 - 已注释，不再查询首次消费金额
            # first_consume_sql = MemberConsumeSqlAddQueries.get_dwoutput_first_consume_amount_sql(start_date, end_date, bid, sid)
            # logger.info(f"first_consume_amount查询SQL: {first_consume_sql}")
            # first_consume_result = await db.execute_dwoutput_one(first_consume_sql)
            # result['first_consume_amount'] = first_consume_result.get('first_consume_amount', 0) if first_consume_result else 0

            # 单独查询消费频次统计
            frequency_stats_sql = MemberConsumeSqlAddQueries.get_dwoutput_consume_frequency_stats_sql(start_date, end_date, bid, sid)
            logger.info(f"consume_frequency_stats查询SQL: {frequency_stats_sql}")
            frequency_stats_result = await db.execute_dwoutput_one(frequency_stats_sql)
            if frequency_stats_result:
                result['consume_once_members'] = frequency_stats_result.get('consume_once_members', 0) or 0
                result['consume_twice_members'] = frequency_stats_result.get('consume_twice_members', 0) or 0
                result['consume_thrice_members'] = frequency_stats_result.get('consume_thrice_members', 0) or 0
                result['consume_more_than_thrice_members'] = frequency_stats_result.get('consume_more_than_thrice_members', 0) or 0
            else:
                result['consume_once_members'] = 0
                result['consume_twice_members'] = 0
                result['consume_thrice_members'] = 0
                result['consume_more_than_thrice_members'] = 0

            query_time = time.time() - start_time
            logger.info(f"dwoutput消费基础数据查询完成，耗时: {query_time:.3f}秒")
            logger.info(f"消费基础数据结果: {result}")

            return result
            
        except Exception as e:
            logger.error(f"获取dwoutput消费基础数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"dwoutput消费基础数据查询失败: {str(e)}")
    
    async def _fetch_dwoutput_consume_detail_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取dwoutput数据库的储值使用详情数据 - 独立查询版本"""
        try:
            logger.info(f"开始查询dwoutput储值使用详情 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            start_time = time.time()
            
            # 独立执行储值使用详情查询
            sid_condition = f"AND sid = '{sid}'" if sid else ""
            
            consume_detail_sql = f"""
            SELECT {MemberConsumeSqlQueries.get_dwoutput_total_prepay_used_real_sql(start_date, end_date, bid, sid)}
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN {start_date} AND {end_date}
            AND bid = {bid}
            {sid_condition}
            """
            logger.info(f"储值使用详情查询SQL: {consume_detail_sql}")
            result = await db.execute_dwoutput_one(consume_detail_sql)
            
            query_time = time.time() - start_time
            logger.info(f"dwoutput储值使用详情查询完成，耗时: {query_time:.3f}秒")
            logger.info(f"储值使用详情结果: {result}")
            
            if not result:
                logger.warning(f"dwoutput储值使用详情查询结果为空: bid={bid}, sid={sid}")
                return {'total_prepay_used_real': 0}
            
            return result
            
        except Exception as e:
            logger.error(f"获取dwoutput储值使用详情失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"dwoutput储值使用详情查询失败: {str(e)}")
    
    async def _fetch_wedatas_coupon_trade_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取wedatas数据库的券带动交易数据 - 独立查询版本"""
        try:
            logger.info(f"开始查询wedatas券交易数据 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            start_time = time.time()
            
            # 独立执行券交易查询
            sid_condition = f"AND sid = '{sid}'" if sid else ""
            
            coupon_trade_sql = f"""
            SELECT {MemberConsumeSqlQueries.get_wedatas_total_coupon_trade_amount_sql(start_date, end_date, bid, sid)}
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN {start_date} AND {end_date}
            AND bid = {bid}
            {sid_condition}
            """
            logger.info(f"券交易查询SQL: {coupon_trade_sql}")
            result = await db.execute_wedatas_one(coupon_trade_sql)
            
            query_time = time.time() - start_time
            logger.info(f"wedatas券交易数据查询完成，耗时: {query_time:.3f}秒")
            logger.info(f"券交易数据结果: {result}")
            
            if not result:
                logger.warning(f"wedatas券交易数据查询结果为空: bid={bid}, sid={sid}")
                return {'total_coupon_trade_amount': 0}
            
            return result
            
        except Exception as e:
            logger.error(f"获取wedatas券交易数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"wedatas券交易数据查询失败: {str(e)}")

    async def _fetch_member_consume_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> Dict[str, Any]:
        """获取会员消费数据 - 重构后的版本"""
        try:
            logger.info(f"开始获取会员消费数据 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            total_start_time = time.time()
            
            # 并行查询三个数据源
            consume_base_data, consume_detail_data, coupon_trade_data = await asyncio.gather(
                self._fetch_dwoutput_consume_base_data(start_date, end_date, bid, sid),
                self._fetch_dwoutput_consume_detail_data(start_date, end_date, bid, sid),
                self._fetch_wedatas_coupon_trade_data(start_date, end_date, bid, sid)
            )
            
            if not consume_base_data:
                logger.warning(f"消费基础数据查询异常: bid={bid}, sid={sid}")
                return {}
            
            # 使用重构后的独立计算函数合并数据
            result = MemberConsumeCalculator.merge_consume_data(
                consume_base_data, consume_detail_data, coupon_trade_data
            )

            # 合并额外的消费数据（复购率和储值消费实收金额占比）
            result = MemberConsumeCalculatorAdd.merge_additional_consume_data(result)

            # 使用重构后的独立函数处理金额字段
            money_fields = [
                'total_consume_amount_virtual', 'total_consume_cash_real', 'total_prepay_real',
                'total_prepay_used_real', 'total_real_income', 'total_coupon_trade_amount',
                # 'first_consume_amount', 'repeat_consume_amount',  # 已注释，不再处理这两个字段
                'avg_contribution_per_user', 'avg_consume_amount'
            ]
            result = MemberConsumeCalculator.process_money_fields(result, money_fields)
            
            total_time = time.time() - total_start_time
            logger.info(f"会员消费数据获取完成，总耗时: {total_time:.3f}秒，数据字段数: {len(result)}")
            
            return result
            
        except Exception as e:
            logger.error(f"获取会员消费数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"数据查询失败: {str(e)}")
    
    def _calculate_change_rate(self, current: float, previous: float) -> str:
        """计算变化率"""
        if previous == 0:
            return "0%" if current == 0 else "100%"
        
        rate = ((current - previous) / previous) * 100
        return f"{rate:+.2f}%"
    
    def _map_db_result_to_response(self, current_data: Dict[str, Any], 
                                  chain_data: Dict[str, Any], 
                                  year_data: Dict[str, Any]) -> MemberConsumeData:
        """将数据库结果映射到响应模型"""
        
        # 映射字段名到数据库字段
        field_mapping = {
            "total_amount": ("total_consume_amount_virtual", "元"),
            "consume_users": ("total_consume_uv", "人"),
            "total_consume_count": ("total_consume_pv", "笔"),
            "actual_amount": ("total_consume_cash_real", "元"),
            "cash_users": ("total_consume_cash_uv", "人"),
            "cash_consume_count": ("total_consume_cash_pv", "笔"),
            "prepay_amount": ("total_prepay_real", "元"),
            "prepay_actual_amount": ("total_prepay_used_real", "元"),
            "prepay_consumption_ratio": ("prepay_consumption_ratio", "%"),
            "prepay_users": ("total_prepay_uv", "人"),
            "prepay_consume_count": ("total_prepay_pv", "笔"),
            "total_actual_amount": ("total_real_income", "元"),
            # "first_consume_amount": ("first_consume_amount", "元"),  # 已注释，不再映射首次消费金额
            # "repeat_consume_amount": ("repeat_consume_amount", "元"),  # 已注释，不再映射再次消费金额
            "avg_contribution": ("avg_contribution_per_user", "元"),
            "consume_frequency": ("consume_frequency", "次/人"),
            "avg_consume_amount": ("avg_consume_amount", "元/笔"),
            "consume_once_members": ("consume_once_members", "人"),
            "consume_twice_members": ("consume_twice_members", "人"),
            "consume_thrice_members": ("consume_thrice_members", "人"),
            "consume_more_than_thrice_members": ("consume_more_than_thrice_members", "人"),
            "repurchase_rate": ("repurchase_rate", "%"),
            "coupon_trade_amount": ("total_coupon_trade_amount", "元")
        }
        
        result = MemberConsumeData()
        
        for field_name, (db_field, unit) in field_mapping.items():
            current_value = current_data.get(db_field, 0) or 0
            chain_value = chain_data.get(db_field, 0) or 0
            year_value = year_data.get(db_field, 0) or 0
            
            # 处理数值类型
            if unit in ["元", "次/人", "元/笔", "%"]:
                # 金额类型、比率类型和百分比类型保留小数
                current_value = float(current_value) if current_value else 0.0
                chain_value = float(chain_value) if chain_value else 0.0
                year_value = float(year_value) if year_value else 0.0
            else:
                # 人数、笔数等整数类型
                current_value = int(current_value) if current_value else 0
                chain_value = int(chain_value) if chain_value else 0
                year_value = int(year_value) if year_value else 0
            
            field_data = FieldDataModel(
                value=current_value,
                unit=unit,
                chain_comparison=[chain_value],
                chain_change_rate=[self._calculate_change_rate(current_value, chain_value)],
                chain_labels=["上期"],
                year_over_year=year_value,
                year_over_year_rate=self._calculate_change_rate(current_value, year_value)
            )
            
            setattr(result, field_name, field_data)
        
        return result
    
    async def get_member_consume_data(self, query_params: QueryParams) -> MemberConsumeData:
        """获取会员消费数据"""
        try:
            logger.info(f"收到会员消费数据查询请求 - bid: {query_params.bid}, sid: {query_params.sid}, 日期: {query_params.start_date}~{query_params.end_date}")
            
            # 计算时间范围
            time_ranges = self._calculate_time_ranges(query_params)
            logger.debug(f"时间范围计算完成: {time_ranges}")
            
            # 并行获取当前期间、环比期间、同比期间的数据
            current_data, chain_data, year_data = await asyncio.gather(
                self._fetch_member_consume_data(
                    time_ranges["current"]["start"],
                    time_ranges["current"]["end"],
                    query_params.bid,
                    query_params.sid
                ),
                self._fetch_member_consume_data(
                    time_ranges["chain"]["start"],
                    time_ranges["chain"]["end"],
                    query_params.bid,
                    query_params.sid
                ),
                self._fetch_member_consume_data(
                    time_ranges["year_over_year"]["start"],
                    time_ranges["year_over_year"]["end"],
                    query_params.bid,
                    query_params.sid
                )
            )
            
            # 映射数据到响应模型
            result = self._map_db_result_to_response(current_data, chain_data, year_data)
            
            logger.info(f"会员消费数据查询成功完成 - bid: {query_params.bid}, sid: {query_params.sid}")
            return result
            
        except Exception as e:
            logger.error(f"获取会员消费数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"获取会员消费数据失败: {str(e)}")


# 创建服务实例
member_consume_service = MemberConsumeService()


@router.post("/member-consume", response_model=ResponseModel)
async def get_member_consume_data(query_params: QueryParams):
    """获取会员消费数据"""
    try:
        logger.info(f"API接收到会员消费数据请求: {query_params}")
        data = await member_consume_service.get_member_consume_data(query_params)
        logger.info("会员消费数据API调用成功")
        return ResponseModel(
            code=200,
            message="获取会员消费数据成功",
            data=data
        )
    except HTTPException as e:
        logger.error(f"HTTP异常: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"获取会员消费数据异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
