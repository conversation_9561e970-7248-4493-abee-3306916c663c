# Context
Filename: 券参数分析任务.md
Created On: 2025-01-08
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
在 `member-api\api\PPTreport` 目录中查找并定位以下参数的设置位置和生成逻辑：

**需要查找的参数列表：**
- 券相关参数（1-10）：coupon_id1-10, coupon_name1-10, coupon_send_count1-10, coupon_usage_rate1-10, coupon_used_count1-10, drive_total_amount1-10
- 汇总统计参数：avg_usage_rate, total_send_count, total_used_count, total_drive_amount

**当前业务逻辑问题：**
现在的券排序和统计逻辑需要修改。

**期望的新业务逻辑：**
1. **券排序逻辑**：券1-10应该按照 `coupon_send_count`（发券量）从高到低排序，只展示发券量最高的前10个券的信息
2. **汇总统计逻辑**：以下四个汇总参数必须基于该商户的**所有券**进行统计计算，而不是仅基于展示的前10个券：
   - `total_send_count`（总发券量）
   - `total_used_count`（总使用量）
   - `avg_usage_rate`（平均使用率）
   - `total_drive_amount`（总带动金额）

**具体任务：**
1. 找到这些参数在代码中的定义和赋值位置
2. 分析当前的数据查询和计算逻辑
3. 识别需要修改的具体代码段，以实现上述新的业务逻辑要求
4. 确保理解当前实现与期望实现之间的差异
5. 注意将单位张、元、%随参数一同输出，同时注意券少于10张的，要将无法填充的券信息置为空

# Project Overview
这是一个会员数据报告PPT生成系统，主要涉及优惠券数据的查询、排序、统计和展示逻辑。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 核心文件结构分析

### 1. 主要相关文件
- **`constants.py`**: 包含券参数生成的核心逻辑（第228-321行）
- **`DataAcquisition.py`**: 负责数据获取和初步处理（第272-334行）
- **`CouponTradeTab.py`**: 原始数据查询模块（第55-154行）
- **`PPTReport.py`**: 主要的报告生成协调器

### 2. 当前券参数生成流程

#### 数据查询层 (`CouponTradeTab.py`)
- **SQL查询**：从 `wedatas_welife_coupon_log` 表查询券数据
- **当前排序逻辑**：按 `trade_cash_amount`（带动现金消费金额）降序排序（第105行）
- **字段映射**：
  - `coupon_sent` → `couponSendCount`（发券量）
  - `coupon_used` → `couponUsedCount`（使用量）
  - `coupon_use_rate` → `couponUsageRate`（使用率）
  - `trade_cash_amount` → `driveCashAmount`（带动现金消费）
  - `trade_total_amount` → `driveTotalAmount`（带动总交易金额）

#### 数据处理层 (`DataAcquisition.py`)
- **`_extract_coupon_data`方法**（第272-334行）：
  - 按 `driveCashAmount` 降序排序（第299-303行）
  - **当前问题**：只保留前8个券（第305行），应该改为前10个
  - **汇总统计问题**：汇总数据基于排序后的前8个券计算（第313-323行）

#### 参数生成层 (`constants.py`)
- **`get_coupon_analysis_params`方法**（第230-321行）：
  - **当前问题**：只处理前8个券，应该改为前10个（第266行）
  - **当前问题**：汇总统计基于展示的券计算，而非所有券（第284-287行）

### 3. 关键问题识别

#### 问题1：排序逻辑错误
- **当前**：按 `driveCashAmount`（带动现金消费金额）排序
- **期望**：按 `couponSendCount`（发券量）排序

#### 问题2：展示数量错误
- **当前**：只展示前8个券
- **期望**：展示前10个券

#### 问题3：汇总统计逻辑错误
- **当前**：汇总统计基于展示的前8个券
- **期望**：汇总统计基于所有券数据

### 4. 参数输出格式分析
- 券参数：`coupon_id1-10`, `coupon_name1-10`, `coupon_send_count1-10`, `coupon_usage_rate1-10`, `coupon_used_count1-10`, `drive_total_amount1-10`
- 汇总参数：`avg_usage_rate`, `total_send_count`, `total_used_count`, `total_drive_amount`
- 单位处理：已正确添加单位（张、元、%）

### 5. 数据流向
1. `CouponTradeTab.get_coupon_trade_data()` → 查询原始数据
2. `DataAcquisition._extract_coupon_data()` → 数据处理和排序
3. `constants.get_coupon_analysis_params()` → 生成PPT参数
4. `PPTReport._convert_to_ppt_params()` → 整合到完整参数集

# Proposed Solution (Populated by INNOVATE mode)

## 解决方案设计

### 方案1：分离排序和汇总逻辑（推荐）
**核心思路**：在数据处理层分别处理展示数据和汇总数据

**优点**：
- 逻辑清晰，展示和统计分离
- 最小化代码修改
- 保持现有架构不变

**实现要点**：
1. 修改 `CouponTradeTab.py` 的排序逻辑：按发券量排序
2. 修改 `DataAcquisition.py`：
   - 先计算所有券的汇总数据
   - 再按发券量排序取前10个用于展示
3. 修改 `constants.py`：
   - 接收分离的展示数据和汇总数据
   - 生成前10个券的参数

### 方案2：双重查询方案
**核心思路**：分别查询展示数据和汇总数据

**优点**：
- 数据准确性最高
- 逻辑完全独立

**缺点**：
- 需要额外的数据库查询
- 性能开销较大

### 方案3：单次查询后处理方案
**核心思路**：一次查询所有数据，在内存中分别处理

**优点**：
- 性能最优
- 数据一致性好

**缺点**：
- 内存占用可能较大（如果券数量很多）

## 推荐方案详细设计

采用**方案1：分离排序和汇总逻辑**

### 修改点1：CouponTradeTab.py
- 修改SQL的ORDER BY子句：从按现金消费排序改为按发券量排序

### 修改点2：DataAcquisition.py
- 在 `_extract_coupon_data` 方法中：
  1. 先基于所有券计算汇总统计
  2. 再按发券量排序取前10个
  3. 返回包含展示数据和汇总数据的结构

### 修改点3：constants.py
- 修改 `get_coupon_analysis_params` 方法：
  1. 处理前10个券（而非8个）
  2. 使用传入的汇总数据（而非重新计算）

### 数据结构设计
```python
{
    "coupon_list": [...],  # 按发券量排序的前10个券
    "summary_stats": {     # 基于所有券的汇总统计
        "total_send_count": xxx,
        "total_used_count": xxx,
        "total_drive_amount": xxx,
        "avg_usage_rate": xxx
    }
}
```

# Implementation Plan (Generated by PLAN mode)

## 详细实施计划

### 第一阶段：修改数据查询排序逻辑

#### 步骤1：修改CouponTradeTab.py的排序逻辑
- **文件**：`member-api\api\query\CouponTradeTab.py`
- **修改位置**：第105行的ORDER BY子句
- **修改内容**：将 `ORDER BY (SUM(trade_cash) - SUM(cancel_trade_cash)) DESC` 改为 `ORDER BY (SUM(coupon_send) - SUM(cancel_coupon_send)) DESC`
- **原因**：按发券量降序排序，而非按带动现金消费金额排序

### 第二阶段：修改数据处理逻辑

#### 步骤2：修改DataAcquisition.py的数据提取逻辑
- **文件**：`member-api\api\PPTreport\DataAcquisition.py`
- **修改位置**：`_extract_coupon_data`方法（第272-334行）
- **修改内容**：
  1. 先基于所有券数据计算汇总统计
  2. 再按发券量排序取前10个券用于展示
  3. 修改返回数据结构，包含展示数据和汇总数据

#### 步骤3：修改constants.py的参数生成逻辑
- **文件**：`member-api\api\PPTreport\constants.py`
- **修改位置**：`get_coupon_analysis_params`方法（第230-321行）
- **修改内容**：
  1. 将循环范围从1-8改为1-10
  2. 使用传入的汇总统计数据，而非重新计算
  3. 确保券少于10个时，剩余位置填充空值

### 第三阶段：测试和验证

#### 步骤4：单元测试验证
- 验证排序逻辑正确性
- 验证汇总统计准确性
- 验证参数生成完整性

#### 步骤5：集成测试
- 测试完整的PPT生成流程
- 验证生成的PPT参数正确性

## Implementation Checklist:
1. 修改CouponTradeTab.py中的SQL排序逻辑（按发券量排序）
2. 修改DataAcquisition.py中的数据提取逻辑（分离汇总和展示逻辑）
3. 修改constants.py中的参数生成逻辑（处理10个券，使用汇总数据）
4. 测试数据查询和排序功能
5. 测试参数生成功能
6. 进行端到端集成测试

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> 已完成核心代码修改，准备进行测试验证

# Task Progress (Appended by EXECUTE mode after each step completion)

## 2025-01-08 核心代码修改完成
- **步骤1**: 修改CouponTradeTab.py中的SQL排序逻辑（按发券量排序）
- **修改内容**: 将ORDER BY从按带动现金消费排序改为按发券量排序
- **文件**: member-api\api\query\CouponTradeTab.py 第105行
- **状态**: ✅ 完成

- **步骤2**: 修改DataAcquisition.py中的数据提取逻辑（分离汇总和展示逻辑）
- **修改内容**:
  1. 先基于所有券计算汇总统计
  2. 再按发券量排序取前10个券用于展示
  3. 修改返回数据结构，包含展示数据和汇总数据
- **文件**: member-api\api\PPTreport\DataAcquisition.py 第272-337行
- **状态**: ✅ 完成

- **步骤3**: 修改constants.py中的参数生成逻辑（处理10个券，使用汇总数据）
- **修改内容**:
  1. 循环范围从1-8改为1-10
  2. 使用传入的汇总统计数据，而非重新计算
  3. 添加单位（张、元、%）到参数输出
  4. 处理券数量不足10个的情况
- **文件**: member-api\api\PPTreport\constants.py 第230-312行
- **状态**: ✅ 完成

- **步骤4**: 修复发券量为0但有使用量的券显示问题
- **修改内容**:
  1. 修改参数生成逻辑，确保有券数据时显示所有数值（包括0）
  2. 发券量为0时显示"0张"，使用率为0时显示"0.0%"
  3. 只有完全无券数据时才显示空值
- **文件**: member-api\api\PPTreport\constants.py 第287-311行
- **状态**: ✅ 完成

# Final Review (Populated by REVIEW mode)
*待完成*
