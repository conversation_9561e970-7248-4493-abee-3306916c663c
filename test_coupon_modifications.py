#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试券参数修改的功能
验证排序逻辑、汇总统计和参数生成是否正确
"""

import sys
import os
import asyncio
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'member-api'))

from api.PPTreport.DataAcquisition import DataAcquisitionService
from api.PPTreport.constants import PPTConstants
from core.models import QueryParams

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_mock_coupon_data():
    """创建模拟的券数据用于测试"""
    return [
        {
            'couponName': '满100减20券',
            'couponId': 'CPN001',
            'couponSendCount': 5000,  # 发券量最高
            'couponUsedCount': 3500,
            'couponUsageRate': 70.0,
            'driveTotalAmount': 150000.0
        },
        {
            'couponName': '新用户专享券',
            'couponId': 'CPN002', 
            'couponSendCount': 3000,  # 发券量第二
            'couponUsedCount': 2100,
            'couponUsageRate': 70.0,
            'driveTotalAmount': 200000.0  # 带动金额最高
        },
        {
            'couponName': '周末特惠券',
            'couponId': 'CPN003',
            'couponSendCount': 2000,  # 发券量第三
            'couponUsedCount': 1600,
            'couponUsageRate': 80.0,
            'driveTotalAmount': 120000.0
        },
        {
            'couponName': '生日券',
            'couponId': 'CPN004',
            'couponSendCount': 1500,
            'couponUsedCount': 900,
            'couponUsageRate': 60.0,
            'driveTotalAmount': 80000.0
        },
        {
            'couponName': '节日券',
            'couponId': 'CPN005',
            'couponSendCount': 1000,
            'couponUsedCount': 700,
            'couponUsageRate': 70.0,
            'driveTotalAmount': 60000.0
        }
    ]

def test_data_extraction():
    """测试数据提取逻辑"""
    logger.info("=== 测试数据提取逻辑 ===")
    
    # 创建数据获取服务实例
    data_service = DataAcquisitionService()
    
    # 创建模拟数据
    mock_data = create_mock_coupon_data()
    
    # 测试数据提取
    result = data_service._extract_coupon_data(mock_data)
    
    logger.info(f"提取结果: {result}")
    
    # 验证结果结构
    assert "coupon_list" in result, "缺少 coupon_list 字段"
    assert "summary_stats" in result, "缺少 summary_stats 字段"
    
    # 验证排序逻辑（按发券量降序）
    coupon_list = result["coupon_list"]
    if len(coupon_list) > 1:
        for i in range(len(coupon_list) - 1):
            current_send = coupon_list[i].get('couponSendCount', 0)
            next_send = coupon_list[i + 1].get('couponSendCount', 0)
            assert current_send >= next_send, f"排序错误: {current_send} < {next_send}"
    
    # 验证汇总统计
    summary = result["summary_stats"]
    expected_total_send = sum(item['couponSendCount'] for item in mock_data)
    expected_total_used = sum(item['couponUsedCount'] for item in mock_data)
    expected_total_drive = sum(item['driveTotalAmount'] for item in mock_data)
    
    assert summary["total_send_count"] == expected_total_send, f"总发券量错误: {summary['total_send_count']} != {expected_total_send}"
    assert summary["total_used_count"] == expected_total_used, f"总使用量错误: {summary['total_used_count']} != {expected_total_used}"
    assert abs(summary["total_drive_amount"] - expected_total_drive) < 0.01, f"总带动金额错误: {summary['total_drive_amount']} != {expected_total_drive}"
    
    logger.info("✅ 数据提取逻辑测试通过")
    return result

def test_parameter_generation():
    """测试参数生成逻辑"""
    logger.info("=== 测试参数生成逻辑 ===")
    
    # 使用数据提取的结果
    data_service = DataAcquisitionService()
    mock_data = create_mock_coupon_data()
    extracted_data = data_service._extract_coupon_data(mock_data)
    
    # 测试参数生成
    params = PPTConstants.get_coupon_analysis_params(extracted_data)
    
    logger.info(f"生成的参数数量: {len(params)}")
    
    # 验证券参数数量（应该有10个券的参数）
    coupon_ids = [k for k in params.keys() if k.startswith('coupon_id')]
    assert len(coupon_ids) == 10, f"券ID参数数量错误: {len(coupon_ids)} != 10"
    
    # 验证汇总参数
    required_summary_params = ['total_send_count', 'total_used_count', 'avg_usage_rate', 'total_drive_amount']
    for param in required_summary_params:
        assert param in params, f"缺少汇总参数: {param}"
        assert params[param], f"汇总参数为空: {param}"
    
    # 验证单位
    assert "张" in params["total_send_count"], "总发券量缺少单位"
    assert "张" in params["total_used_count"], "总使用量缺少单位"
    assert "%" in params["avg_usage_rate"], "平均使用率缺少单位"
    assert "元" in params["total_drive_amount"], "总带动金额缺少单位"
    
    # 验证排序（第一个券应该是发券量最高的）
    assert params["coupon_name1"] == "满100减20券", f"排序错误: {params['coupon_name1']} != 满100减20券"
    
    # 验证空值处理（第6-10个券应该为空）
    for i in range(6, 11):
        assert params[f"coupon_id{i}"] == "", f"第{i}个券ID应该为空"
        assert params[f"coupon_name{i}"] == "", f"第{i}个券名称应该为空"
    
    logger.info("✅ 参数生成逻辑测试通过")
    
    # 打印关键参数用于验证
    logger.info("=== 关键参数验证 ===")
    logger.info(f"券1: {params['coupon_name1']} - 发券量: {params['coupon_send_count1']}")
    logger.info(f"券2: {params['coupon_name2']} - 发券量: {params['coupon_send_count2']}")
    logger.info(f"券3: {params['coupon_name3']} - 发券量: {params['coupon_send_count3']}")
    logger.info(f"总发券量: {params['total_send_count']}")
    logger.info(f"总使用量: {params['total_used_count']}")
    logger.info(f"平均使用率: {params['avg_usage_rate']}")
    logger.info(f"总带动金额: {params['total_drive_amount']}")
    
    return params

def main():
    """主测试函数"""
    try:
        logger.info("开始测试券参数修改功能...")
        
        # 测试数据提取
        test_data_extraction()
        
        # 测试参数生成
        test_parameter_generation()
        
        logger.info("🎉 所有测试通过！券参数修改功能正常工作")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        raise

if __name__ == "__main__":
    main()
